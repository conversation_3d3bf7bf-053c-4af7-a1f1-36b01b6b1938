# Excel Encryption API

A Node.js server with APIs to encrypt and decrypt Excel files, plus functionality to import Excel data into a database.

## Setup

1. Install dependencies:
```
npm install
```

2. Start the encryption/decryption server:
```
npm start
```

3. Start the Excel import API server:
```
npm run excel-api
```

The encryption server will run on `http://localhost:3000`.
The Excel import server will run on `http://localhost:3001`.

## Features

### 1. Excel File Encryption/Decryption APIs (Port 3000)
### 2. Excel Data Import to Database APIs (Port 3001)

## API Endpoints

## Encryption/Decryption APIs (Port 3000)

### 1. Encrypt Excel File

**Endpoint:** `POST /encrypt`

**Request:**
- Form data with:
  - `file`: The Excel file to encrypt
  - `key` (optional): Custom encryption key (defaults to 'default-encryption-key')

**Response:**
```json
{
  "message": "Excel file encrypted successfully",
  "originalFile": "example.xlsx",
  "encryptedFile": "encrypted-example.xlsx"
}
```

### 2. Decrypt Excel File

**Endpoint:** `POST /decrypt`

**Request:**
- Form data with:
  - `file`: The encrypted Excel file
  - `key` (optional): The same key used for encryption

**Response:**
```json
{
  "message": "Excel file decrypted successfully",
  "encryptedFile": "encrypted-example.xlsx",
  "decryptedFile": "decrypted-example.xlsx"
}
```

## Excel Import APIs (Port 3001)

### 1. Import Excel to Database

**Endpoint:** `POST /import-excel`

**Request:**
- Form data with:
  - `file`: The Excel file to import
  - `tableName` (optional): Database table name (defaults to 'excel_import')

**Response:**
```json
{
  "message": "Excel data imported successfully",
  "originalFile": "data.xlsx",
  "result": {
    "success": true,
    "successCount": 150,
    "errorCount": 0,
    "tableName": "excel_import",
    "totalRows": 150
  }
}
```

### 2. Import Excel with Advanced Options

**Endpoint:** `POST /import-excel-advanced`

**Request:**
- Form data with:
  - `file`: The Excel file to import
  - `tableName` (optional): Database table name (defaults to 'excel_import')
  - `dropTableFirst` (optional): 'true' to drop existing table (defaults to 'false')
  - `batchSize` (optional): Number of rows per batch (defaults to 100)

**Response:**
```json
{
  "message": "Excel data imported successfully with advanced options",
  "originalFile": "data.xlsx",
  "options": {
    "tableName": "my_data",
    "dropTableFirst": true,
    "batchSize": 50
  },
  "result": {
    "success": true,
    "successCount": 150,
    "tableName": "my_data"
  }
}
```

### 3. Preview Excel Data

**Endpoint:** `POST /preview-excel`

**Request:**
- Form data with:
  - `file`: The Excel file to preview

**Response:**
```json
{
  "message": "Excel file preview",
  "originalFile": "data.xlsx",
  "headers": ["Name", "Email", "Age"],
  "totalRows": 150,
  "preview": [
    {"Name": "John", "Email": "<EMAIL>", "Age": 25},
    {"Name": "Jane", "Email": "<EMAIL>", "Age": 30}
  ],
  "sanitizedColumns": [
    {"original": "Name", "sanitized": "name"},
    {"original": "Email", "sanitized": "email"},
    {"original": "Age", "sanitized": "age"}
  ]
}
```

## Excel to Database Import

The `insert-excel-data-to-db.js` module provides functionality to read Excel files and automatically insert the data into a MySQL database.

### Configuration

Update the database configuration in `insert-excel-data-to-db.js`:

```javascript
const dbConfig = {
  host: 'localhost',
  user: 'your_username',
  password: 'your_password',
  database: 'your_database'
};
```

### Usage

#### 1. API Server Mode
```bash
npm run excel-api
```

#### 2. Command Line Usage
```bash
npm run excel-to-db
```

#### 3. Programmatic Usage
```javascript
const { insertExcelDataToDB, insertExcelDataWithConfig } = require('./insert-excel-data-to-db');

// Basic usage
const result = await insertExcelDataToDB('./data.xlsx', 'my_table');

// Advanced usage with options
const result = await insertExcelDataWithConfig('./data.xlsx', {
  tableName: 'my_table',
  dropTableFirst: true,
  batchSize: 100
});
```

### Features

- **Automatic Table Creation**: Creates database table based on Excel headers
- **Column Name Sanitization**: Converts Excel headers to valid SQL column names
- **Data Type Detection**: Automatically detects and assigns appropriate MySQL data types
- **Batch Processing**: Supports batch inserts for better performance
- **Error Handling**: Continues processing even if some rows fail
- **Progress Tracking**: Shows import progress for large files
- **Preview Mode**: Preview data without importing to database

### Excel File Requirements

- First row must contain headers (column names)
- Headers will be used as database column names (automatically sanitized)
- Supports common data types: text, numbers, dates
- Empty cells are handled as NULL values

## Testing with Postman

### For Encryption/Decryption (Port 3000):
1. Open Postman
2. Create a new POST request to `http://localhost:3000/encrypt`
3. In the Body tab, select "form-data"
4. Add a key named "file", change type to "File", and select your Excel file
5. Optionally add another key named "key" with your custom encryption key
6. Send the request

### For Excel Import (Port 3001):
1. Open Postman
2. Create a new POST request to `http://localhost:3001/import-excel`
3. In the Body tab, select "form-data"
4. Add a key named "file", change type to "File", and select your Excel file
5. Optionally add "tableName" key with your desired table name
6. Send the request

## File Structure

```
├── encrypt-excel.js              # Main API server for encryption/decryption (Port 3000)
├── insert-excel-data-to-db.js    # Excel to database import API server (Port 3001)
├── package.json                  # Dependencies and scripts
├── README.md                     # This file
├── uploads/                      # Temporary uploaded files
├── encrypted/                    # Encrypted Excel files
└── decrypted/                    # Decrypted Excel files
```

## Note

- The encrypted files are stored in the "encrypted" folder, and decrypted files are stored in the "decrypted" folder.
- Make sure to use the exact field name "file" when uploading files to avoid "Unexpected field" errors. 