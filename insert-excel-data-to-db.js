const mysql = require('mysql2/promise');
const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');
const express = require('express');
const multer = require('multer');

// Express app setup
const app = express();
const port = 3001;

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({ 
  storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB file size limit
});

// Ensure upload directory exists
if (!fs.existsSync('uploads')) {
  fs.mkdirSync('uploads');
}

// Database configuration
const dbConfig = {
  host: 'ls-7bb1814d45950b0b88a0a260dcc2b31cc2e5cb1d.c7fzrrj3jmvz.us-east-1.rds.amazonaws.com',
  user: 'dbmasteruser',
  password: 'k$EAh9n)A:cPq(*{E-^F=A]W]jjGq~Xo',
  database: 'staging-widget-service'
};

// Function to sanitize column names for SQL
function sanitizeColumnName(name) {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9_]/g, '_') // Replace non-alphanumeric chars with underscore
    .replace(/^[0-9]/, '_$&')    // Prefix with underscore if starts with number
    .replace(/_+/g, '_')         // Replace multiple underscores with single
    .replace(/^_|_$/g, '');      // Remove leading/trailing underscores
}

// Function to get MySQL data type based on value
function getMySQLDataType(value) {
  if (typeof value === 'number') {
    return Number.isInteger(value) ? 'INT' : 'DECIMAL(10,2)';
  } else if (value instanceof Date) {
    return 'DATETIME';
  } else {
    return 'VARCHAR(255)';
  }
}

// Function to read Excel file and extract data
function readExcelFile(filePath) {
  try {
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // Use first sheet
    const worksheet = workbook.Sheets[sheetName];
    
    // Convert sheet to JSON with header row as keys
    const data = xlsx.utils.sheet_to_json(worksheet);
    
    if (data.length === 0) {
      throw new Error('Excel file is empty or has no data rows');
    }
    
    // Get headers from the first row
    const headers = Object.keys(data[0]);
    
    return { headers, data };
  } catch (error) {
    throw new Error(`Failed to read Excel file: ${error.message}`);
  }
}

// Function to create table dynamically based on Excel headers
async function createTableFromHeaders(connection, tableName, headers, sampleData) {
  const sanitizedColumns = headers.map(header => {
    const sanitizedName = sanitizeColumnName(header);
    const sampleValue = sampleData[header];
    const dataType = getMySQLDataType(sampleValue);
    return `\`${sanitizedName}\` ${dataType}`;
  });
  
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS \`${tableName}\` (
      id INT AUTO_INCREMENT PRIMARY KEY,
      ${sanitizedColumns.join(',\n      ')},
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `;
  
  console.log('Creating table with SQL:', createTableSQL);
  await connection.execute(createTableSQL);
  return headers.map(header => sanitizeColumnName(header));
}

// Function to insert data into database
async function insertExcelDataToDB(filePath, tableName = 'reference_data_products_widget_new') {
  let connection;
  
  try {
    // Read Excel file
    console.log('Reading Excel file:', filePath);
    const { headers, data } = readExcelFile(filePath);
    console.log(`Found ${data.length} rows with headers:`, headers);
    
    // Connect to database
    console.log('Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    // Create table dynamically
    console.log('Creating/checking table:', tableName);
    const sanitizedColumns = await createTableFromHeaders(connection, tableName, headers, data[0]);
    
    // Prepare insert statement
    const placeholders = sanitizedColumns.map(() => '?').join(', ');
    const insertSQL = `INSERT INTO \`${tableName}\` (${sanitizedColumns.map(col => `\`${col}\``).join(', ')}) VALUES (${placeholders})`;
    
    console.log('Insert SQL:', insertSQL);
    
    // Insert data row by row
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < data.length; i++) {
      try {
        const row = data[i];
        const values = headers.map(header => {
          let value = row[header];
          
          // Handle different data types
          if (value === null || value === undefined || value === '') {
            return null;
          }
          
          // Convert Excel date numbers to proper dates
          if (typeof value === 'number' && value > 25569 && value < 73050) {
            // Likely an Excel date (between 1970 and 2000)
            const date = new Date((value - 25569) * 86400 * 1000);
            if (!isNaN(date.getTime())) {
              return date.toISOString().slice(0, 19).replace('T', ' ');
            }
          }
          
          return value;
        });
        
        await connection.execute(insertSQL, values);
        successCount++;
        
        if ((i + 1) % 100 === 0) {
          console.log(`Processed ${i + 1}/${data.length} rows...`);
        }
      } catch (error) {
        console.error(`Error inserting row ${i + 1}:`, error.message);
        console.error('Row data:', data[i]);
        errorCount++;
      }
    }
    
    console.log(`\nImport completed:`);
    console.log(`- Successfully inserted: ${successCount} rows`);
    console.log(`- Errors: ${errorCount} rows`);
    console.log(`- Table: ${tableName}`);
    
    return {
      success: true,
      successCount,
      errorCount,
      tableName,
      totalRows: data.length
    };
    
  } catch (error) {
    console.error('Error:', error.message);
    return {
      success: false,
      error: error.message
    };
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Function to insert Excel data with custom configuration
async function insertExcelDataWithConfig(filePath, options = {}) {
  const {
    tableName = 'excel_import',
    dropTableFirst = false,
    batchSize = 100
  } = options;
  
  let connection;
  
  try {
    console.log('Reading Excel file:', filePath);
    const { headers, data } = readExcelFile(filePath);
    
    connection = await mysql.createConnection(dbConfig);
    
    // Drop table if requested
    if (dropTableFirst) {
      console.log('Dropping existing table...');
      await connection.execute(`DROP TABLE IF EXISTS \`${tableName}\``);
    }
    
    // Create table
    const sanitizedColumns = await createTableFromHeaders(connection, tableName, headers, data[0]);
    
    // Batch insert for better performance
    const placeholders = sanitizedColumns.map(() => '?').join(', ');
    const insertSQL = `INSERT INTO \`${tableName}\` (${sanitizedColumns.map(col => `\`${col}\``).join(', ')}) VALUES (${placeholders})`;
    
    let successCount = 0;
    
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      
      try {
        await connection.beginTransaction();
        
        for (const row of batch) {
          const values = headers.map(header => row[header] || null);
          await connection.execute(insertSQL, values);
          successCount++;
        }
        
        await connection.commit();
        console.log(`Inserted batch: ${Math.min(i + batchSize, data.length)}/${data.length}`);
        
      } catch (error) {
        await connection.rollback();
        console.error(`Error in batch starting at row ${i + 1}:`, error.message);
      }
    }
    
    return { success: true, successCount, tableName };
    
  } catch (error) {
    return { success: false, error: error.message };
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// API ENDPOINTS

// API endpoint for importing Excel file to database
app.post('/import-excel', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ 
        error: 'No file uploaded',
        message: 'Please upload an Excel file with field name "file"'
      });
    }
    
    const tableName = req.body.tableName || 'excel_import';
    const result = await insertExcelDataToDB(req.file.path, tableName);
    
    // Clean up uploaded file
    fs.unlinkSync(req.file.path);
    
    res.json({
      message: 'Excel data imported successfully',
      originalFile: req.file.originalname,
      result: result
    });
  } catch (error) {
    console.error('Import error:', error);
    res.status(500).json({ 
      error: 'Failed to import Excel data', 
      details: error.message 
    });
  }
});

// API endpoint for importing Excel file with advanced options
app.post('/import-excel-advanced', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ 
        error: 'No file uploaded',
        message: 'Please upload an Excel file with field name "file"'
      });
    }
    
    const options = {
      tableName: req.body.tableName || 'excel_import',
      dropTableFirst: req.body.dropTableFirst === 'true',
      batchSize: parseInt(req.body.batchSize) || 100
    };
    
    const result = await insertExcelDataWithConfig(req.file.path, options);
    
    // Clean up uploaded file
    fs.unlinkSync(req.file.path);
    
    res.json({
      message: 'Excel data imported successfully with advanced options',
      originalFile: req.file.originalname,
      options: options,
      result: result
    });
  } catch (error) {
    console.error('Import error:', error);
    res.status(500).json({ 
      error: 'Failed to import Excel data', 
      details: error.message 
    });
  }
});

// API endpoint to preview Excel data without importing
app.post('/preview-excel', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ 
        error: 'No file uploaded',
        message: 'Please upload an Excel file with field name "file"'
      });
    }
    
    const { headers, data } = readExcelFile(req.file.path);
    
    // Return first 5 rows for preview
    const preview = data.slice(0, 5);
    
    // Clean up uploaded file
    fs.unlinkSync(req.file.path);
    
    res.json({
      message: 'Excel file preview',
      originalFile: req.file.originalname,
      headers: headers,
      totalRows: data.length,
      preview: preview,
      sanitizedColumns: headers.map(h => ({
        original: h,
        sanitized: sanitizeColumnName(h)
      }))
    });
  } catch (error) {
    console.error('Preview error:', error);
    res.status(500).json({ 
      error: 'Failed to preview Excel data', 
      details: error.message 
    });
  }
});

// Start the server
app.listen(port, () => {
  console.log(`Excel to DB Import API server running at http://localhost:${port}`);
  console.log('Available endpoints:');
  console.log('- POST /import-excel - Import Excel file to database');
  console.log('- POST /import-excel-advanced - Import with advanced options');
  console.log('- POST /preview-excel - Preview Excel data without importing');
});

// Example usage for command line
async function main() {
  // Example: Insert data from Excel file
  const excelFilePath = './sample-data.xlsx'; // Change this to your Excel file path
  
  if (fs.existsSync(excelFilePath)) {
    console.log('Starting Excel to DB import...');
    const result = await insertExcelDataToDB(excelFilePath, 'imported_data');
    console.log('Result:', result);
  } else {
    console.log('Excel file not found. Please provide a valid Excel file path.');
    console.log('Usage examples:');
    console.log('1. insertExcelDataToDB("./data.xlsx", "my_table")');
    console.log('2. insertExcelDataWithConfig("./data.xlsx", { tableName: "my_table", dropTableFirst: true })');
  }
}

// Export functions for use in other modules
module.exports = {
  insertExcelDataToDB,
  insertExcelDataWithConfig,
  readExcelFile,
  sanitizeColumnName
};

// Run if called directly
if (require.main === module) {
  // Check if we want to run as API server or command line
  const args = process.argv.slice(2);
  if (args.includes('--server') || args.length === 0) {
    // Start API server
    console.log('Starting API server mode...');
  } else {
    // Run command line version
    main().catch(console.error);
  }
} 