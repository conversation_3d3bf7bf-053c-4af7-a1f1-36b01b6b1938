{"name": "excel-encryption-api", "version": "1.0.0", "description": "API for encrypting and decrypting Excel files", "main": "encrypt-excel.js", "scripts": {"start": "node encrypt-excel.js", "dev": "nodemon encrypt-excel.js", "excel-to-db": "node insert-excel-data-to-db.js", "excel-api": "node insert-excel-data-to-db.js --server"}, "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "xlsx": "^0.18.5", "mysql2": "^3.14.1"}, "devDependencies": {"nodemon": "^2.0.22"}}